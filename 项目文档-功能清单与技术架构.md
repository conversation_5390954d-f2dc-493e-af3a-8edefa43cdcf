# AI Process Text Info 项目文档

## 项目概述

**AI Process Text Info** 是一个基于 Spring Boot 的智能标牌信息处理系统，专门用于煤矿设备标牌信息的AI智能分析和分类处理。系统通过集成大语言模型（LLM），实现对标牌备注信息的自动化解析、分类和结构化存储。

## 功能清单

### 1. 核心业务功能

#### 1.1 标牌信息智能分析
- **单产品处理**: 根据产品编号处理单个产品的标牌分类信息
- **批量处理**: 支持多个产品编号的批量标牌信息处理
- **高效批量处理**: 优化的批量处理机制，提升处理效率
- **智能分类**: 自动识别并分类为支架标牌、立柱标牌、镂空标牌、配件标牌等类型

#### 1.2 标牌类型分类
- **支架标牌**: 包含支架型号、工作阻力、支护强度、支护高度、重量等信息
- **立柱标牌**: 包含工作压力、工作阻力、物料号、安标编号等信息
- **镂空标牌**: 包含施工号、支架镂空编号牌、立柱镂空编号牌等信息
- **配件标牌**: 包含配件标牌号、物料号、名称、图号等信息
- **自订货处理**: 专门处理自订货整架相关的标牌信息

#### 1.3 数据持久化
- **分类结果存储**: 将AI分析结果保存到数据库
- **历史记录管理**: 维护标牌处理的历史记录
- **处理统计**: 记录处理成功率、耗时等统计信息

### 2. AI智能对话功能

#### 2.1 智能助手
- **AI对话**: 支持与AI助手进行标牌相关的智能对话
- **流式对话**: 支持实时流式AI对话响应
- **工具调用**: AI助手可调用系统工具函数执行具体任务
- **智能分析**: 根据用户查询进行智能标牌分析

#### 2.2 AI工具函数
- **标牌查询**: 根据产品编号查询标牌基础信息
- **智能分析**: 对标牌信息进行AI分析和分类
- **数据检索**: 查询已保存的支架标牌和立柱标牌详细信息
- **统计报告**: 提供标牌处理的统计数据
- **模糊搜索**: 支持关键词模糊搜索标牌信息

### 3. 定时任务管理

#### 3.1 自动化处理
- **定时执行**: 支持Cron表达式配置的定时任务
- **批量处理**: 自动批量处理待处理的标牌信息
- **数据迁移**: 自动执行数据迁移和查询优化
- **错误重试**: 支持失败重试机制

#### 3.2 任务监控
- **状态监控**: 实时监控任务执行状态
- **性能监控**: 监控处理性能和资源使用情况
- **日志记录**: 详细的任务执行日志
- **配置管理**: 动态配置任务参数

### 4. 数据管理功能

#### 4.1 多数据源支持
- **主数据源**: 用于存储处理结果和系统数据
- **第二数据源**: 用于读取原始标牌信息
- **数据同步**: 支持数据源间的数据同步

#### 4.2 数据查询
- **统计查询**: 提供各类标牌的统计信息
- **历史查询**: 查询历史处理记录
- **条件查询**: 支持多条件组合查询

### 5. Web界面功能

#### 5.1 测试工具
- **聊天测试**: ChatController API测试工具
- **处理测试**: ProcessController API测试工具
- **定时任务管理**: 定时任务状态监控和配置管理界面

#### 5.2 用户界面
- **响应式设计**: 支持多设备访问
- **实时反馈**: 实时显示处理结果和状态
- **操作日志**: 详细的操作日志记录

## 技术架构

### 1. 整体架构

```
┌─────────────────────────────────────────────────────────────┐
│                        前端层 (Frontend)                      │
├─────────────────────────────────────────────────────────────┤
│  • HTML5 + CSS3 + JavaScript                               │
│  • 响应式设计                                                │
│  • AJAX异步通信                                             │
│  • 流式数据处理                                              │
└─────────────────────────────────────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────┐
│                      控制层 (Controller)                     │
├─────────────────────────────────────────────────────────────┤
│  • ProcessController - 标牌处理API                          │
│  • ChatController - AI对话API                               │
│  • ScheduleController - 定时任务管理API                      │
└─────────────────────────────────────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────┐
│                      服务层 (Service)                        │
├─────────────────────────────────────────────────────────────┤
│  • SignboardProcessService - 标牌处理服务                    │
│  • SignboardBatchProcessService - 批量处理服务               │
│  • SignboardAiToolService - AI工具服务                      │
│  • 各类实体服务 (BracketSignboardInfoService等)             │
└─────────────────────────────────────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────┐
│                      AI集成层 (AI Layer)                     │
├─────────────────────────────────────────────────────────────┤
│  • LangChain4j框架                                          │
│  • OpenAI兼容API                                            │
│  • Ollama本地模型支持                                        │
│  • 流式响应处理                                              │
│  • 工具函数调用                                              │
└─────────────────────────────────────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────┐
│                    数据访问层 (Data Access)                   │
├─────────────────────────────────────────────────────────────┤
│  • Spring Data JPA                                         │
│  • 多数据源配置                                              │
│  • Repository模式                                           │
│  • 事务管理                                                  │
└─────────────────────────────────────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────┐
│                      数据层 (Database)                       │
├─────────────────────────────────────────────────────────────┤
│  • Oracle数据库 (主数据源)                                   │
│  • Oracle数据库 (第二数据源)                                 │
│  • MySQL支持 (可选)                                          │
└─────────────────────────────────────────────────────────────┘
```

### 2. 技术栈详情

#### 2.1 后端技术栈
- **框架**: Spring Boot 3.3.10
- **Java版本**: Java 17
- **数据访问**: Spring Data JPA + Hibernate
- **数据库**: Oracle 11g (主要), MySQL 8.0 (可选)
- **连接池**: HikariCP
- **AI集成**: LangChain4j 1.0.0-beta2
- **构建工具**: Gradle 7.x
- **日志**: SLF4J + Logback

#### 2.2 AI技术栈
- **AI框架**: LangChain4j
- **模型支持**: 
  - OpenAI兼容API (Qwen3-30B-A3B)
  - Ollama本地模型
  - DeepSeek模型
- **功能特性**:
  - 流式响应 (Streaming)
  - 工具函数调用 (Function Calling)
  - RAG支持 (Easy RAG)
  - Redis向量存储

#### 2.3 前端技术栈
- **基础技术**: HTML5 + CSS3 + JavaScript (ES6+)
- **样式**: CSS Grid + Flexbox + 渐变设计
- **交互**: Fetch API + 流式数据处理
- **响应式**: 移动端适配

#### 2.4 数据库设计
- **多数据源架构**:
  - 主数据源: 存储处理结果和系统数据
  - 第二数据源: 读取原始标牌信息
- **核心表结构**:
  - `ZJPJ_BP_LOG`: 原始标牌信息表
  - `ZJPJ_BP_LOG_LOCAL`: 本地标牌信息表
  - `ZJPJ_BP_LOG_LOCAL_HIS`: 历史记录表
  - `signboard_process_info`: 处理记录表
  - `signboard_classification_info`: 分类结果表
  - `bracket_signboard_info`: 支架标牌信息表
  - `column_signboard_info`: 立柱标牌信息表
  - `hollow_signboard_info`: 镂空标牌信息表
  - `accessory_signboard_info`: 配件标牌信息表

### 3. 系统特性

#### 3.1 高可用性
- **多数据源**: 支持主备数据源切换
- **事务管理**: 分布式事务支持
- **错误处理**: 完善的异常处理机制
- **重试机制**: 失败自动重试

#### 3.2 高性能
- **批量处理**: 优化的批量处理算法
- **连接池**: HikariCP高性能连接池
- **异步处理**: 支持异步任务处理
- **流式响应**: 实时流式数据传输

#### 3.3 可扩展性
- **模块化设计**: 清晰的分层架构
- **配置化**: 丰富的配置选项
- **插件化**: 支持自定义处理器
- **多模型支持**: 支持多种AI模型

#### 3.4 可监控性
- **日志系统**: 详细的日志记录
- **性能监控**: 处理性能统计
- **状态监控**: 实时状态监控
- **错误追踪**: 完整的错误追踪链

### 4. 部署架构

#### 4.1 系统要求
- **操作系统**: CentOS 7+
- **Java环境**: Java 17+
- **内存要求**: 16GB+
- **磁盘空间**: 200GB+

#### 4.2 部署方式
- **单机部署**: 支持单机部署
- **容器化**: 支持Docker容器化部署
- **集群部署**: 支持多实例集群部署

#### 4.3 配置管理
- **多环境配置**: 支持开发、测试、生产环境配置
- **外部配置**: 支持外部配置文件
- **动态配置**: 支持运行时配置更新

## 项目优势

1. **智能化程度高**: 集成先进的大语言模型，实现高精度的文本分析和分类
2. **处理效率高**: 优化的批量处理机制，支持大规模数据处理
3. **扩展性强**: 模块化设计，易于扩展新的标牌类型和处理逻辑
4. **用户体验好**: 直观的Web界面，支持实时交互和流式响应
5. **稳定性强**: 完善的错误处理和重试机制，确保系统稳定运行
6. **监控完善**: 全面的监控和日志系统，便于运维管理

## 应用场景

- **煤矿设备管理**: 煤矿企业的设备标牌信息管理
- **制造业质量管理**: 制造企业的产品标牌信息处理
- **设备维护管理**: 设备维护过程中的标牌信息识别
- **数据标准化**: 非结构化标牌信息的结构化处理
